"use server"

import { revalidatePath, revalidateTag } from "next/cache"
import { savePeriod } from "../data/index"
import { appraisalPeriodFormSchema } from "../schemas"
import {
  requirePermission,
  checkRateLimit,
  logUserAction,
  validateSession
} from "../auth"
import {
  handleServerActionError,
  RateLimitError
} from "./shared"
import { db } from "../db"
import type { AppraisalPeriod } from "../types"
import { getMonthStartEnd, formatPeriodName, matchesPeriodMonth } from "../utils/period-helpers"

export async function savePeriodAction(formData: FormData) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'period-save', 5, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('period:write')

    // Extract and validate data
    const rawData = {
      id: formData.get('id') as string || undefined,
      periodStart: formData.get('periodStart') as string,
      periodEnd: formData.get('periodEnd') as string,
      closed: formData.get('closed') === 'true',
    }

    const validatedData = appraisalPeriodFormSchema.parse(rawData)

    // Log the action
    await logUserAction('period:save', {
      periodStart: validatedData.periodStart,
      periodEnd: validatedData.periodEnd,
      isUpdate: !!rawData.id
    })

    // Save to database
    await savePeriod(validatedData)

    // Revalidate cache
    revalidatePath("/dashboard/periods")

    return { success: true, message: "Appraisal Period saved successfully." }
  } catch (error) {
    return handleServerActionError(error)
  }
}

/**
 * Server action to find existing period or create new one for the given month and year
 */
export async function findOrCreatePeriodAction(month: number, year: number): Promise<AppraisalPeriod> {
  try {
    const session = await validateSession()
    
    // Check rate limiting
    if (!checkRateLimit(session.userId, 'period:create', 10, 60000)) {
      throw new RateLimitError()
    }
    
    const { startDate, endDate } = getMonthStartEnd(month, year)
    const periodName = formatPeriodName(month, year)
    
    // First, try to find existing period
    const existingPeriods = await db.getAppraisalPeriods()
    const existingPeriod = existingPeriods.find(period => matchesPeriodMonth(period, month, year))
    
    if (existingPeriod) {
      console.log(`Found existing period: ${periodName} (${existingPeriod.id})`)
      return existingPeriod
    }
    
    // Create new period if none exists
    console.log(`Creating new period: ${periodName}`)
    const newPeriod = await db.createAppraisalPeriod({
      name: periodName,
      startDate,
      endDate,
      status: 'active'
    })
    
    // Revalidate relevant cache tags
    revalidateTag('periods')
    revalidateTag('appraisals')
    
    console.log(`Created new period: ${periodName} (${newPeriod.id})`)
    return newPeriod
    
  } catch (error) {
    console.error(`Error finding or creating period for ${formatPeriodName(month, year)}:`, error)
    throw new Error(`Failed to find or create appraisal period for ${formatPeriodName(month, year)}`)
  }
}

/**
 * Server action to get or create period ID for a specific month and year
 */
export async function getOrCreatePeriodIdAction(month: number, year: number): Promise<string> {
  const period = await findOrCreatePeriodAction(month, year)
  return period.id
}

/**
 * Server action to check if a period exists for the given month and year
 */
export async function periodExistsForMonthAction(month: number, year: number): Promise<boolean> {
  try {
    const session = await validateSession()
    
    // Check rate limiting
    if (!checkRateLimit(session.userId, 'period:read', 30, 60000)) {
      throw new RateLimitError()
    }
    
    const existingPeriods = await db.getAppraisalPeriods()
    const existingPeriod = existingPeriods.find(period => matchesPeriodMonth(period, month, year))
    
    return !!existingPeriod
  } catch (error) {
    console.error('Error checking if period exists:', error)
    return false
  }
}

/**
 * Server action to get period for specific month/year
 */
export async function getPeriodForMonthAction(month: number, year: number): Promise<AppraisalPeriod | null> {
  try {
    const session = await validateSession()
    
    // Check rate limiting
    if (!checkRateLimit(session.userId, 'period:read', 30, 60000)) {
      throw new RateLimitError()
    }
    
    const existingPeriods = await db.getAppraisalPeriods()
    const existingPeriod = existingPeriods.find(period => matchesPeriodMonth(period, month, year))
    
    return existingPeriod || null
  } catch (error) {
    console.error('Error getting period for month:', error)
    return null
  }
}