import type { AppraisalPeriod } from '../types'

export interface MonthYearOption {
  value: string
  label: string
  month: number
  year: number
}

/**
 * Generate all month/year combinations for the period selector
 * Covers from January 2024 to December 2026
 */
export function generateMonthYearOptions(): MonthYearOption[] {
  const options: MonthYearOption[] = []
  const startYear = 2024
  const endYear = 2026
  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ]

  for (let year = startYear; year <= endYear; year++) {
    for (let month = 0; month < 12; month++) {
      options.push({
        value: `${year}-${String(month + 1).padStart(2, '0')}`,
        label: `${months[month]} ${year}`,
        month: month + 1, // 1-based month
        year
      })
    }
  }

  return options
}

/**
 * Get current month and year as formatted string
 */
export function getCurrentMonthYear(): string {
  const now = new Date()
  const year = now.getFullYear()
  const month = now.getMonth() + 1 // 1-based month
  return `${year}-${String(month).padStart(2, '0')}`
}

/**
 * Format period name from month and year
 */
export function formatPeriodName(month: number, year: number): string {
  if (!Number.isInteger(month) || month < 1 || month > 12) {
    throw new Error(`formatPeriodName: month must be an integer between 1 and 12, got ${month}`)
  }

  if (!Number.isInteger(year) || year < 0) {
    throw new Error(`formatPeriodName: year must be a non-negative integer, got ${year}`)
  }

  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ]
  return `${months[month - 1]} ${year}`
}

/**
 * Get start and end dates for a given month and year
 */
export function getMonthStartEnd(month: number, year: number): { startDate: string; endDate: string } {
  const startDate = new Date(year, month - 1, 1)
  const endDate = new Date(year, month, 0) // Last day of month
  
  return {
    startDate: startDate.toISOString().split('T')[0], // YYYY-MM-DD format
    endDate: endDate.toISOString().split('T')[0]
  }
}

/**
 * Parse month/year value string (YYYY-MM) into month and year numbers
 */
export function parseMonthYear(value: string): { month: number; year: number } {
  if (!value || typeof value !== 'string') {
    throw new Error('parseMonthYear: value must be a non-empty string')
  }

  // Validate format with regex
  const formatRegex = /^\d{4}-\d{2}$/
  if (!formatRegex.test(value)) {
    throw new Error(`parseMonthYear: value "${value}" must match format YYYY-MM`)
  }

  const parts = value.split('-')
  if (parts.length !== 2) {
    throw new Error(`parseMonthYear: value "${value}" must contain exactly one dash`)
  }

  const [yearStr, monthStr] = parts
  const year = parseInt(yearStr, 10)
  const month = parseInt(monthStr, 10)

  if (isNaN(year) || isNaN(month)) {
    throw new Error(`parseMonthYear: unable to parse numbers from "${value}"`)
  }

  if (year < 0) {
    throw new Error(`parseMonthYear: year ${year} must be non-negative`)
  }

  if (month < 1 || month > 12) {
    throw new Error(`parseMonthYear: month ${month} must be between 1 and 12`)
  }

  return {
    month,
    year
  }
}

/**
 * Helper function to check if a period matches the specified month and year
 */
export function matchesPeriodMonth(period: AppraisalPeriod, month: number, year: number): boolean {
  try {
    const periodStart = new Date(period.periodStart)
    const periodEnd = new Date(period.periodEnd)
    
    // Get the target month's start and end dates
    const { startDate, endDate } = getMonthStartEnd(month, year)
    const targetStart = new Date(startDate)
    const targetEnd = new Date(endDate)
    
    // Check if the period covers the same month/year
    return (
      periodStart.getFullYear() === targetStart.getFullYear() &&
      periodStart.getMonth() === targetStart.getMonth() &&
      periodEnd.getFullYear() === targetEnd.getFullYear() &&
      periodEnd.getMonth() === targetEnd.getMonth()
    )
  } catch (error) {
    // If there's any error in date parsing, assume no match
    console.error('Error comparing period dates:', error)
    return false
  }
}

// Note: Database-dependent period functions have been moved to lib/actions/periods.ts
// as server actions to prevent client-side database imports