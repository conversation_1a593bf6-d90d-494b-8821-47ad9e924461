"use client"

import { useState, useTransition } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useRouter } from "next/navigation"
import { useAutosave } from "@/hooks/use-autosave"
import { saveAppraisalDraftAction, submitAppraisalAction, resubmitAppraisalRevisionAction } from "@/lib/actions"
import { appraisalSubmissionSchema, type AppraisalSubmission } from "@/lib/schemas"
import type { AppraisalDetails, EmployeeDetails } from "@/lib/types"

export function useAppraisalForm(
  employee: EmployeeDeta<PERSON>, 
  appraisal: AppraisalDetails | null,
  options?: { periodMonth?: number; periodYear?: number }
) {
  const router = useRouter()
  const [isPending, startTransition] = useTransition()
  const [submitError, setSubmitError] = useState<string | null>(null)
  const [submitSuccess, setSubmitSuccess] = useState(false)

  const [formData, setFormData] = useState<Partial<AppraisalDetails>>(() => {
    const safeAppraisal = appraisal ? {
      q1: appraisal.q1 ?? null,
      q2: appraisal.q2 ?? false,
      q3: appraisal.q3 ?? "",
      q4: appraisal.q4 ?? "",
      q5: appraisal.q5 ?? "",
      paymentStatus: appraisal.paymentStatus ?? null,
      keyContributions: appraisal.keyContributions ?? "",
      extraInitiatives: appraisal.extraInitiatives ?? "",
      performanceLacking: appraisal.performanceLacking ?? "",
      disciplineRating: appraisal.disciplineRating ?? null,
      disciplineComment: appraisal.disciplineComment ?? "",
      daysOffTaken: appraisal.daysOffTaken ?? null,
      impactRating: appraisal.impactRating ?? null,
      impactComment: appraisal.impactComment ?? "",
      qualityRating: appraisal.qualityRating ?? null,
      qualityComment: appraisal.qualityComment ?? "",
      collaborationRating: appraisal.collaborationRating ?? null,
      collaborationComment: appraisal.collaborationComment ?? "",
      skillGrowthRating: appraisal.skillGrowthRating ?? null,
      skillGrowthComment: appraisal.skillGrowthComment ?? "",
      readinessPromotion: appraisal.readinessPromotion ?? null,
      readinessComment: appraisal.readinessComment ?? "",
    } : {
      q1: null,
      q2: false,
      q3: "",
      q4: "",
      q5: "",
      paymentStatus: null,
      keyContributions: "",
      extraInitiatives: "",
      performanceLacking: "",
      disciplineRating: null,
      disciplineComment: "",
      daysOffTaken: null,
      impactRating: null,
      impactComment: "",
      qualityRating: null,
      qualityComment: "",
      collaborationRating: null,
      collaborationComment: "",
      skillGrowthRating: null,
      skillGrowthComment: "",
      readinessPromotion: null,
      readinessComment: "",
    }

    console.log('🔄 [DEBUG] Initializing form data:', safeAppraisal)
    return safeAppraisal
  })

  const submissionForm = useForm<Omit<AppraisalSubmission, 'id' | 'periodId' | 'employeeId' | 'managerId' | 'status'>>({
    resolver: zodResolver(appraisalSubmissionSchema.omit({
      id: true,
      periodId: true,
      employeeId: true,
      managerId: true,
      status: true
    })),
    defaultValues: {
      q1: appraisal?.q1 ?? undefined,
      q2: Boolean(appraisal?.q2) ?? false,
      q3: String(appraisal?.q3 ?? ""),
      q4: String(appraisal?.q4 ?? ""),
      q5: String(appraisal?.q5 ?? ""),
      paymentStatus: appraisal?.paymentStatus ?? undefined,
      keyContributions: String(appraisal?.keyContributions ?? ""),
      extraInitiatives: String(appraisal?.extraInitiatives ?? ""),
      performanceLacking: String(appraisal?.performanceLacking ?? ""),
      disciplineRating: appraisal?.disciplineRating ?? undefined,
      disciplineComment: String(appraisal?.disciplineComment ?? ""),
      daysOffTaken: appraisal?.daysOffTaken ?? undefined,
      impactRating: appraisal?.impactRating ?? undefined,
      impactComment: String(appraisal?.impactComment ?? ""),
      qualityRating: appraisal?.qualityRating ?? undefined,
      qualityComment: String(appraisal?.qualityComment ?? ""),
      collaborationRating: appraisal?.collaborationRating ?? undefined,
      collaborationComment: String(appraisal?.collaborationComment ?? ""),
      skillGrowthRating: appraisal?.skillGrowthRating ?? undefined,
      skillGrowthComment: String(appraisal?.skillGrowthComment ?? ""),
      readinessPromotion: appraisal?.readinessPromotion ?? undefined,
      readinessComment: String(appraisal?.readinessComment ?? ""),
    },
  })

  const autosave = useAutosave({
    data: formData,
    onSave: async (data, signal) => {
      // Ensure autosave data is clean and serializable
      try {
        JSON.stringify(data)
      } catch (error) {
        console.warn('[AUTOSAVE] Skipping autosave due to non-serializable data:', error)
        return { success: false, error: 'Data not serializable' }
      }

      const draftData = {
        ...data,
        employeeId: employee.id,
        periodId: appraisal?.periodId || '',
        managerId: '',
      }
      return saveAppraisalDraftAction(draftData, options)
    },
    interval: 3000,
    maxRetries: 3,
    enabled: !submitSuccess && appraisal?.status !== 'submitted',
    onError: (error, retryCount) => {
      console.error(`Autosave failed (attempt ${retryCount}):`, error)
    },
    onSuccess: () => {
      console.log('Draft saved successfully')
    },
  })

  const handleInputChange = (
    field: keyof Omit<AppraisalDetails, "id" | "periodId" | "employeeId" | "managerId" | "status">,
    value: any,
  ) => {
    // Ensure the value is serializable by creating a clean copy
    let cleanValue = value

    // Handle different value types to ensure serializability
    if (value && typeof value === 'object') {
      // If it's an event object, extract the target value
      if (value.target && 'value' in value.target) {
        cleanValue = value.target.value
      } else if (value.currentTarget && 'value' in value.currentTarget) {
        cleanValue = value.currentTarget.value
      } else {
        // For other objects, try to create a clean copy
        try {
          cleanValue = JSON.parse(JSON.stringify(value))
        } catch (error) {
          console.warn(`[FORM] Non-serializable value for field ${field}:`, error)
          cleanValue = String(value) // Fallback to string conversion
        }
      }
    }

    console.log(`🔄 [DEBUG] Setting field ${field} to:`, cleanValue)
    setFormData((prev) => ({ ...prev, [field]: cleanValue }))
    submissionForm.setValue(field as any, cleanValue, { shouldValidate: true })
  }

  const handleSubmit = async () => {
    setSubmitError(null)

    const isValid = await submissionForm.trigger()
    if (!isValid) {
      const errors = submissionForm.formState.errors
      console.log('Form validation errors:', errors)
      setSubmitError('Please fill in all required fields correctly before submitting.')
      return
    }

    startTransition(async () => {
      try {
        // Create clean submission data with explicit type conversion
        const submissionData = {
          q1: formData.q1,
          q2: Boolean(formData.q2),
          q3: String(formData.q3 || ''),
          q4: String(formData.q4 || ''),
          q5: String(formData.q5 || ''),
          paymentStatus: formData.paymentStatus,
          keyContributions: String(formData.keyContributions || ''),
          extraInitiatives: String(formData.extraInitiatives || ''),
          performanceLacking: String(formData.performanceLacking || ''),
          disciplineRating: Number(formData.disciplineRating) || 1,
          disciplineComment: String(formData.disciplineComment || ''),
          daysOffTaken: Number(formData.daysOffTaken) || 0,
          impactRating: Number(formData.impactRating) || 1,
          impactComment: String(formData.impactComment || ''),
          qualityRating: Number(formData.qualityRating) || 1,
          qualityComment: String(formData.qualityComment || ''),
          collaborationRating: Number(formData.collaborationRating) || 1,
          collaborationComment: String(formData.collaborationComment || ''),
          skillGrowthRating: Number(formData.skillGrowthRating) || 1,
          skillGrowthComment: String(formData.skillGrowthComment || ''),
          readinessPromotion: formData.readinessPromotion || 'no-not-yet',
          readinessComment: String(formData.readinessComment || ''),
          employeeId: employee.id,
          periodId: appraisal?.periodId || '',
          managerId: '',
          status: 'submitted' as const,
          ...(appraisal?.id && { id: appraisal.id }),
        }

        // Validate that the data is serializable before proceeding
        try {
          JSON.stringify(submissionData)
          console.log('✅ [DEBUG] Submission data is serializable')
        } catch (serializationError) {
          console.error('❌ [DEBUG] Submission data is not serializable:', serializationError)
          throw new Error('Form data contains non-serializable values. Please refresh the page and try again.')
        }

        try {
          console.log('📋 [DEBUG] Form submission data:', JSON.stringify(submissionData, null, 2))
        } catch (jsonError) {
          console.log('📋 [DEBUG] Form submission data (raw):', submissionData)
          console.log('📋 [DEBUG] JSON stringify error:', jsonError)
        }

        const result = appraisal?.isRevision
          ? await resubmitAppraisalRevisionAction(submissionData)
          : await submitAppraisalAction(submissionData, options)

        if (result.success) {
          setSubmitSuccess(true)
          setSubmitError(null)
          // Use Next.js router to refresh the page data without a full reload
          // This prevents the "Error in input stream" issue
          router.refresh()
        } else {
          setSubmitError('error' in result ? result.error : 'Failed to submit appraisal')
        }
      } catch (error) {
        console.error('Submission error:', error)
        setSubmitError('An unexpected error occurred. Please try again.')
      }
    })
  }

  return {
    formData,
    submissionForm,
    autosave,
    isPending,
    submitError,
    submitSuccess,
    handleInputChange,
    handleSubmit,
    setSubmitError,
    setSubmitSuccess,
  }
}