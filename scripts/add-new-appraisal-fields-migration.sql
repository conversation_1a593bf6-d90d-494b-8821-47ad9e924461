-- Add new fields to appy_appraisals table for updated appraisal form
-- This migration adds support for new fields while preserving existing data

-- Add new columns to appy_appraisals table
ALTER TABLE appy_appraisals 
ADD COLUMN IF NOT EXISTS project_description TEXT,
ADD COLUMN IF NOT EXISTS took_days_off BOOLEAN,
ADD COLUMN IF NOT EXISTS days_off_dates TEXT,
ADD COLUMN IF NOT EXISTS payment_note TEXT;

-- Update payment_status enum to include new options
-- Note: This will create a new enum and update the column
DO $$ 
BEGIN
    -- Check if the new enum values already exist
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'do-not-pay' 
        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'payment_status')
    ) THEN
        ALTER TYPE payment_status ADD VALUE 'do-not-pay';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'other' 
        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'payment_status')
    ) THEN
        ALTER TYPE payment_status ADD VALUE 'other';
    END IF;
END $$;

-- Add comments to document the new fields
COMMENT ON COLUMN appy_appraisals.project_description IS 'Combined field for project description, areas for improvement, and goals (replaces q3, q4, q5)';
COMMENT ON COLUMN appy_appraisals.took_days_off IS 'Boolean indicating if employee took days off during the period';
COMMENT ON COLUMN appy_appraisals.days_off_dates IS 'Text field specifying the dates when employee took time off';
COMMENT ON COLUMN appy_appraisals.payment_note IS 'Optional note regarding payment decision';

-- Update the updated_at column to current timestamp for tracking
ALTER TABLE appy_appraisals ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

-- Create an index for the new boolean field for performance
CREATE INDEX IF NOT EXISTS idx_appy_appraisals_took_days_off ON appy_appraisals(took_days_off);

-- Log the migration
INSERT INTO public.schema_migrations (version, description, applied_at) 
VALUES (
    'add_new_appraisal_fields_' || to_char(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISS'),
    'Add new appraisal fields: project_description, took_days_off, days_off_dates, payment_note and extend payment_status enum',
    CURRENT_TIMESTAMP
) ON CONFLICT DO NOTHING;

-- Verify the changes
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'appy_appraisals' 
AND column_name IN ('project_description', 'took_days_off', 'days_off_dates', 'payment_note')
ORDER BY ordinal_position;