"use client"

import { useState } from "react"
import { AppraisalDashboardWrapper } from "@/components/appraisal-dashboard-wrapper"
import { formatPeriodName, getCurrentMonthYear, parseMonthYear } from "@/lib/utils/period-helpers"
import type { EmployeeAppraisal, Manager, AppraisalPeriod } from "@/lib/types"

interface DynamicDashboardContentProps {
  appraisals: EmployeeAppraisal[]
  managers: Manager[]
  periods: AppraisalPeriod[]
}

interface DynamicDashboardHeaderProps {
  selectedPeriodName: string
  onPeriodChange: (periodName: string) => void
}

export function DynamicDashboardHeader({ selectedPeriodName }: { selectedPeriodName: string }) {
  return (
    <div className="mb-4 sm:mb-6">
      <h1 className="mobile-heading-1 tracking-tight">Manager Dashboard</h1>
      <p className="mobile-body text-muted-foreground mt-1">{selectedPeriodName} appraisal period overview and team management.</p>
    </div>
  )
}

export function DynamicDashboardContent({ appraisals, managers, periods }: DynamicDashboardContentProps) {
  // Initialize with current month/year
  const currentPeriod = getCurrentMonthYear()
  const { month, year } = parseMonthYear(currentPeriod)
  const [selectedPeriodName, setSelectedPeriodName] = useState<string>(formatPeriodName(month, year))

  const handlePeriodChange = (periodName: string) => {
    setSelectedPeriodName(periodName)
  }

  return (
    <>
      {/* Dynamic Header */}
      <DynamicDashboardHeader selectedPeriodName={selectedPeriodName} />

      {/* Appraisals Section */}
      <div className="space-y-4">
        <h3 className="mobile-heading-2">Team Appraisals</h3>
        {appraisals.length === 0 ? (
          <div className="text-center py-6 sm:py-8">
            <div>
              <h4 className="text-sm sm:text-base font-medium mb-2">No appraisals started</h4>
              <p className="text-muted-foreground text-xs sm:text-sm px-4">
                Appraisals for your team members will appear here once the appraisal period begins.
              </p>
            </div>
          </div>
        ) : (
          <AppraisalDashboardWrapper 
            data={appraisals} 
            managers={managers} 
            periods={periods}
            onPeriodChange={handlePeriodChange}
          />
        )}
      </div>
    </>
  )
}