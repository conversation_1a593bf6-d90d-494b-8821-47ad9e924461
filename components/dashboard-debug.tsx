"use client"

import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

interface DebugData {
  user?: any
  allEmployees?: { count: number; sample: any[] }
  managerEmployees?: { count: number; sample: any[] }
  error?: string
}

export function DashboardDebug() {
  const [debugData, setDebugData] = useState<DebugData>({})
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    let isMounted = true
    ;(async () => {
      try {
        const res = await fetch('/api/test-employees')
        const contentType = res.headers.get('content-type') || ''
        if (!res.ok || !contentType.includes('application/json')) {
          if (isMounted) setDebugData({ error: 'Debug endpoint unavailable (404)' })
          return
        }
        const data = await res.json()
        if (isMounted) setDebugData(data)
      } catch (_) {
        if (isMounted) setDebugData({ error: 'Debug data unavailable' })
      } finally {
        if (isMounted) setLoading(false)
      }
    })()
    return () => { isMounted = false }
  }, [])

  if (process.env.NODE_ENV === 'production') {
    return null // Don't show in production
  }

  return (
    <Card className="border-yellow-200 bg-yellow-50">
      <CardHeader>
        <CardTitle className="text-sm text-yellow-800">🧪 Debug Info</CardTitle>
      </CardHeader>
      <CardContent className="text-xs space-y-2">
        {loading ? (
          <p>Loading debug data...</p>
        ) : (
          <>
            <div>
              <strong>User:</strong> {debugData.user?.fullName} ({debugData.user?.role})
            </div>
            <div>
              <strong>All Employees:</strong> {debugData.allEmployees?.count || 0}
            </div>
            <div>
              <strong>Manager Employees:</strong> {debugData.managerEmployees?.count || 0}
            </div>
            {debugData.error && (
              <div className="text-red-600">
                <strong>Error:</strong> {debugData.error}
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}
