"use client"

import { useState, useEffect } from "react"
import { Calendar, ChevronDown } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import {
  generateMonthYearOptions,
  getCurrentMonthYear,
  parseMonthYear,
  type MonthYearOption
} from "@/lib/utils/period-helpers"

interface PeriodMonthFilterProps {
  value?: string // YYYY-MM format
  onChange: (value: string, month: number, year: number) => void
  disabled?: boolean
  showPeriodStatus?: boolean
  showExternalBadges?: boolean // Show status badges outside the select dropdown
  existingPeriods?: string[] // Array of existing period IDs for the month/year
  className?: string
}

export function PeriodMonthFilter({
  value,
  onChange,
  disabled = false,
  showPeriodStatus = false,
  showExternalBadges = true,
  existingPeriods = [],
  className
}: PeriodMonthFilterProps) {
  const [selectedValue, setSelectedValue] = useState<string>(() => {
    return value || getCurrentMonthYear()
  })
  const [options, setOptions] = useState<MonthYearOption[]>([])

  // Generate options on component mount
  useEffect(() => {
    const monthYearOptions = generateMonthYearOptions()
    setOptions(monthYearOptions)
  }, [])

  // Update selected value when prop changes
  useEffect(() => {
    if (value && value !== selectedValue) {
      setSelectedValue(value)
    }
  }, [value, selectedValue])

  // Handle selection change
  const handleValueChange = (newValue: string) => {
    setSelectedValue(newValue)
    const { month, year } = parseMonthYear(newValue)
    onChange(newValue, month, year)
  }

  // Get current selection details
  const currentOption = options.find(opt => opt.value === selectedValue)
  const currentMonth = currentOption?.month || new Date().getMonth() + 1
  const currentYear = currentOption?.year || new Date().getFullYear()

  // Check if period exists for current selection
  const periodExists = existingPeriods.includes(selectedValue)
  const currentMonthYear = getCurrentMonthYear()
  const isCurrentMonth = selectedValue === currentMonthYear

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Select
        value={selectedValue}
        onValueChange={handleValueChange}
        disabled={disabled}
      >
        <SelectTrigger className="w-[200px]">
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <SelectValue placeholder="Select Period" />
          </div>
        </SelectTrigger>
        <SelectContent className="max-h-60">
          {options.map((option) => {
            const isOptionCurrent = option.value === currentMonthYear
            const optionPeriodExists = existingPeriods.includes(option.value)
            
            return (
              <SelectItem key={option.value} value={option.value}>
                <div className="flex items-center justify-between w-full">
                  <span>{option.label}</span>
                  <div className="flex items-center gap-1 ml-2">
                    {isOptionCurrent && (
                      <Badge variant="outline" className="text-xs bg-blue-100 text-blue-800">
                        Current
                      </Badge>
                    )}
                    {showPeriodStatus && optionPeriodExists && (
                      <Badge variant="outline" className="text-xs bg-green-100 text-green-800">
                        Period Exists
                      </Badge>
                    )}
                    {showPeriodStatus && !optionPeriodExists && (
                      <Badge variant="outline" className="text-xs bg-gray-100 text-gray-600">
                        Auto-create
                      </Badge>
                    )}
                  </div>
                </div>
              </SelectItem>
            )
          })}
        </SelectContent>
      </Select>

      {/* Status indicators */}
      {showExternalBadges && (
        <div className="flex items-center gap-1">
          {isCurrentMonth && (
            <Badge variant="outline" className="bg-blue-100 text-blue-800">
              Current
            </Badge>
          )}
          {showPeriodStatus && periodExists && (
            <Badge variant="outline" className="bg-green-100 text-green-800">
              Period Active
            </Badge>
          )}
          {showPeriodStatus && !periodExists && (
            <Badge variant="outline" className="bg-orange-100 text-orange-800">
              Auto-create
            </Badge>
          )}
        </div>
      )}
    </div>
  )
}

/**
 * Compact version of the period filter for use in smaller spaces
 */
export function CompactPeriodMonthFilter({
  value,
  onChange,
  disabled = false,
  className
}: Omit<PeriodMonthFilterProps, 'showPeriodStatus' | 'existingPeriods' | 'showExternalBadges'>) {
  return (
    <PeriodMonthFilter
      value={value}
      onChange={onChange}
      disabled={disabled}
      showPeriodStatus={false}
      showExternalBadges={false}
      className={className}
    />
  )
}

/**
 * Helper hook for managing period month filter state
 */
export function usePeriodMonthFilter(initialValue?: string) {
  const [selectedPeriod, setSelectedPeriod] = useState(initialValue || getCurrentMonthYear())
  const [selectedMonth, setSelectedMonth] = useState<number>(() => {
    const { month } = parseMonthYear(initialValue || getCurrentMonthYear())
    return month
  })
  const [selectedYear, setSelectedYear] = useState<number>(() => {
    const { year } = parseMonthYear(initialValue || getCurrentMonthYear())
    return year
  })

  const handlePeriodChange = (value: string, month: number, year: number) => {
    setSelectedPeriod(value)
    setSelectedMonth(month)
    setSelectedYear(year)
  }

  return {
    selectedPeriod,
    selectedMonth,
    selectedYear,
    handlePeriodChange,
    reset: () => {
      const current = getCurrentMonthYear()
      const { month, year } = parseMonthYear(current)
      setSelectedPeriod(current)
      setSelectedMonth(month)
      setSelectedYear(year)
    }
  }
}