"use client"

import type { AppraisalFormData, AppraisalInput<PERSON><PERSON>e<PERSON><PERSON><PERSON>, FormError } from "@/components/appraisal/types"

type PerformanceAssessmentSectionProps = {
  formData: AppraisalFormData
  onInputChange: AppraisalInputChangeHandler
  disabled: boolean
  errors: {}
}

export function PerformanceAssessmentSection({
  formData,
  onInputChange,
  disabled,
  errors,
}: PerformanceAssessmentSectionProps) {
  // This section has been removed from the new appraisal form
  // All fields (keyContributions, extraInitiatives, performanceLacking, daysOffTaken) 
  // are preserved in the database but no longer shown in the UI
  return null
}