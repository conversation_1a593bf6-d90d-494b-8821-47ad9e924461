"use client"

import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Textarea } from "@/components/ui/textarea"
import { appraisalQuestions } from "@/lib/constants/appraisal-questions"
import { overallPerformanceOptions } from "@/lib/constants/appraisal-ratings"
import type { AppraisalFormData, AppraisalInputChangeHandler, FormError } from "@/components/appraisal/types"
import type { AppraisalDetails } from "@/lib/types"

type BasicReviewSectionProps = {
  formData: AppraisalFormData
  onInputChange: AppraisalInputChangeHandler
  disabled: boolean
  errors: {
    q1?: FormError
    projectDescription?: FormError
  }
}

export function BasicReviewSection({
  formData,
  onInputChange,
  disabled,
  errors,
}: BasicReviewSectionProps) {
  return (
    <div className="mobile-section">
      <fieldset className="space-y-2">
        <legend className="mobile-body font-medium">
          {appraisalQuestions.q1} <span className="text-red-500" aria-label="required">*</span>
        </legend>
        <RadioGroup
          value={formData.q1 ?? ""}
          onValueChange={(value) => onInputChange("q1", value as AppraisalDetails["q1"])}
          className="flex flex-col space-y-3 pt-2 sm:flex-row sm:space-y-0 sm:space-x-4"
          disabled={disabled}
          aria-required="true"
          aria-describedby={errors.q1 ? "q1-error" : undefined}
        >
          {overallPerformanceOptions.map((option) => (
            <div key={option.value} className="flex items-center space-x-2 touch-target">
              <RadioGroupItem value={option.value} id={`q1-${option.value}`} className="touch-target-sm" />
              <Label htmlFor={`q1-${option.value}`} className="mobile-body cursor-pointer">{option.label}</Label>
            </div>
          ))}
        </RadioGroup>
        {errors.q1?.message && (
          <p
            id="q1-error"
            className="text-sm text-red-500"
            role="alert"
            aria-live="polite"
          >
            {errors.q1.message}
          </p>
        )}
      </fieldset>

      <div className="space-y-2">
        <Label htmlFor="projectDescription" className="mobile-body font-medium">
          {appraisalQuestions.projectDescription} <span className="text-gray-500">(optional)</span>
        </Label>
        <Textarea
          id="projectDescription"
          value={formData.projectDescription ?? ""}
          onChange={(e) => onInputChange("projectDescription", e.target.value)}
          placeholder="Describe your primary project or focus area, any current areas that need improvement, and what your goals are for next month..."
          rows={6}
          disabled={disabled}
          className="mobile-body"
        />
        {errors.projectDescription?.message && (
          <p className="mobile-caption text-red-500">{errors.projectDescription.message}</p>
        )}
      </div>
    </div>
  )
}