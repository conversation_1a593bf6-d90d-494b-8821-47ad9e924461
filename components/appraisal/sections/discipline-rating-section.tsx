"use client"

import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Input } from "@/components/ui/input"
import { RatingField } from "@/components/appraisal/rating-field"
import { appraisalQuestions } from "@/lib/constants/appraisal-questions"
import type { AppraisalFormData, AppraisalInputChangeHandler, FormError } from "@/components/appraisal/types"

type DisciplineRatingSectionProps = {
  formData: AppraisalFormData
  onInputChange: AppraisalInputChangeHandler
  disabled: boolean
  errors: {
    tookDaysOff?: FormError
    daysOffDates?: FormError
    disciplineRating?: FormError
    disciplineComment?: FormError
  }
}

export function DisciplineRatingSection({
  formData,
  onInputChange,
  disabled,
  errors,
}: DisciplineRatingSectionProps) {
  return (
    <div className="space-y-8 border-t pt-8">
      <h3 className="text-lg font-semibold">📅 Attendance</h3>
      
      {/* Attendance Section */}
      <div className="space-y-4">
        <fieldset className="space-y-3">
          <legend className="mobile-body font-medium">
            {appraisalQuestions.tookDaysOff} <span className="text-gray-500">(optional)</span>
          </legend>
          <RadioGroup
            value={formData.tookDaysOff === null ? "" : formData.tookDaysOff ? "yes" : "no"}
            onValueChange={(value) => onInputChange("tookDaysOff", value === "yes" ? true : value === "no" ? false : null)}
            className="flex flex-col space-y-2 pt-2 sm:flex-row sm:space-y-0 sm:space-x-6"
            disabled={disabled}
            aria-describedby={errors.tookDaysOff ? "tookDaysOff-error" : undefined}
          >
            <div className="flex items-center space-x-2 touch-target">
              <RadioGroupItem value="yes" id="tookDaysOff-yes" className="touch-target-sm" />
              <Label htmlFor="tookDaysOff-yes" className="mobile-body cursor-pointer">Yes</Label>
            </div>
            <div className="flex items-center space-x-2 touch-target">
              <RadioGroupItem value="no" id="tookDaysOff-no" className="touch-target-sm" />
              <Label htmlFor="tookDaysOff-no" className="mobile-body cursor-pointer">No</Label>
            </div>
          </RadioGroup>
          {errors.tookDaysOff?.message && (
            <p
              id="tookDaysOff-error"
              className="text-sm text-red-500"
              role="alert"
              aria-live="polite"
            >
              {errors.tookDaysOff.message}
            </p>
          )}
        </fieldset>

        {/* Conditional date field */}
        {formData.tookDaysOff === true && (
          <div className="space-y-2 pl-0 sm:pl-6">
            <Label htmlFor="daysOffDates" className="mobile-body font-medium">
              Please specify the dates:
            </Label>
            <Input
              id="daysOffDates"
              value={formData.daysOffDates ?? ""}
              onChange={(e) => onInputChange("daysOffDates", e.target.value)}
              placeholder="e.g., Dec 15-16, Dec 23"
              disabled={disabled}
              className="mobile-body"
            />
            {errors.daysOffDates?.message && (
              <p className="mobile-caption text-red-500">{errors.daysOffDates.message}</p>
            )}
          </div>
        )}
      </div>

      {/* Discipline Rating Section */}
      <div className="space-y-4">
        <RatingField
          question={appraisalQuestions.disciplineRating}
          value={formData.disciplineRating ?? null}
          onRatingChange={(value) => onInputChange("disciplineRating", value)}
          comment={formData.disciplineComment}
          onCommentChange={(value) => onInputChange("disciplineComment", value)}
          commentLabel="Comment (optional):"
          commentPlaceholder="Any concerns with presence or conduct..."
          disabled={disabled}
          required={false}
          error={errors.disciplineRating?.message}
          commentRequired={false}
          commentError={errors.disciplineComment?.message}
        />
      </div>
    </div>
  )
}