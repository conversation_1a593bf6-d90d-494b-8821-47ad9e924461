"use client"

import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Textarea } from "@/components/ui/textarea"
import { paymentStatusOptions } from "@/lib/constants/appraisal-ratings"
import { appraisalQuestions } from "@/lib/constants/appraisal-questions"
import type { AppraisalFormData, AppraisalInputChangeHandler, FormError } from "@/components/appraisal/types"

type PaymentStatusSectionProps = {
  formData: AppraisalFormData
  onInputChange: AppraisalInputChangeHandler
  disabled: boolean
  errors: {
    paymentStatus?: FormError
    paymentNote?: FormError
  }
}

export function PaymentStatusSection({
  formData,
  onInputChange,
  disabled,
  errors,
}: PaymentStatusSectionProps) {
  return (
    <div className="space-y-6 border-t pt-8">
      <h3 className="text-lg font-semibold">💰 Payment Status</h3>
      
      <div className="space-y-4">
        <fieldset className="space-y-3">
          <legend className="mobile-body font-medium">
            {appraisalQuestions.paymentStatus} <span className="text-red-500">*</span>
          </legend>
          <RadioGroup
            value={formData.paymentStatus || ""}
            onValueChange={(value) => onInputChange("paymentStatus", value as "ready-to-pay" | "contact-manager" | "do-not-pay" | "other" | null)}
            className="flex flex-col space-y-3"
            disabled={disabled}
            aria-required="true"
            aria-describedby={errors.paymentStatus ? "paymentStatus-error" : undefined}
          >
            {paymentStatusOptions.map((option) => (
              <div key={option.value} className="flex items-center space-x-2 touch-target">
                <RadioGroupItem value={option.value} id={`paymentStatus-${option.value}`} className="touch-target-sm" />
                <Label htmlFor={`paymentStatus-${option.value}`} className="mobile-body cursor-pointer">{option.label}</Label>
              </div>
            ))}
          </RadioGroup>
          {errors.paymentStatus?.message && (
            <p
              id="paymentStatus-error"
              className="text-sm text-red-500"
              role="alert"
              aria-live="polite"
            >
              {errors.paymentStatus.message}
            </p>
          )}
        </fieldset>

        <div className="space-y-2">
          <Label htmlFor="paymentNote" className="mobile-body font-medium">
            {appraisalQuestions.paymentNote} <span className="text-gray-500">(optional)</span>
          </Label>
          <Textarea
            id="paymentNote"
            value={formData.paymentNote ?? ""}
            onChange={(e) => onInputChange("paymentNote", e.target.value)}
            placeholder="Additional notes regarding payment decision..."
            rows={3}
            disabled={disabled}
            className="mobile-body"
          />
          {errors.paymentNote?.message && (
            <p className="mobile-caption text-red-500">{errors.paymentNote.message}</p>
          )}
        </div>
      </div>
    </div>
  )
}