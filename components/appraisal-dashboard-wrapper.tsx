"use client"

import { useState, useEffect } from "react"
import { AppraisalDashboardTable } from "@/components/appraisal-dashboard-table"
import { bulkAppraisalAction, getManagerAppraisalsByPeriodAction, findOrCreatePeriodAction } from "@/lib/actions"
import { getCurrentMonthYear } from "@/lib/utils/period-helpers"
import { toast } from "sonner"
import type { EmployeeAppraisal, Manager, AppraisalPeriod } from "@/lib/types"

interface AppraisalDashboardWrapperProps {
  data: EmployeeAppraisal[]
  managers: Manager[]
  periods: AppraisalPeriod[]
  onPeriodChange?: (periodName: string) => void
}

export function AppraisalDashboardWrapper({ data: initialData, managers, periods, onPeriodChange }: AppraisalDashboardWrapperProps) {
  const [data, setData] = useState<EmployeeAppraisal[]>(initialData)
  const [selectedPeriodId, setSelectedPeriodId] = useState<string>("")
  const [selectedPeriodValue, setSelectedPeriodValue] = useState<string>(getCurrentMonthYear())
  const [loading, setLoading] = useState(false)

  // Set default to current active period
  useEffect(() => {
    const activePeriod = periods.find(p => !p.closed)
    if (activePeriod && !selectedPeriodId) {
      setSelectedPeriodId(activePeriod.id)
    }
  }, [periods, selectedPeriodId])

  const handlePeriodChange = async (periodValue: string, month: number, year: number) => {
    if (periodValue === selectedPeriodValue) return
    
    setLoading(true)
    setSelectedPeriodValue(periodValue)
    
    try {
      // Find or create the period for the selected month/year
      const period = await findOrCreatePeriodAction(month, year)
      setSelectedPeriodId(period.id)
      
      // Load appraisals for this period
      const result = await getManagerAppraisalsByPeriodAction(period.id)
      if (result.success && 'data' in result) {
        setData(result.data)
      } else {
        toast.error('error' in result ? result.error : 'Failed to load appraisals for selected period')
      }
      
      // Notify parent component about period change
      if (onPeriodChange) {
        const periodName = `${new Date(year, month - 1).toLocaleDateString('en-US', { month: 'long' })} ${year}`
        onPeriodChange(periodName)
      }
    } catch (error) {
      console.error('Failed to load appraisals for period:', error)
      toast.error('Failed to load appraisals for selected period')
    } finally {
      setLoading(false)
    }
  }

  const handleBulkAction = async (action: string, selectedIds: string[]) => {
    try {
      const result = await bulkAppraisalAction(action, selectedIds)
      if (result.success) {
        toast.success(result.message || 'Action completed successfully')
        // Revalidate the page to show updated data
        window.location.reload()
      } else {
        toast.error(result.error || 'Action failed')
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
      console.error('Bulk action error:', error)
    }
  }

  return (
    <AppraisalDashboardTable 
      data={data} 
      managers={managers} 
      periods={periods}
      selectedPeriodId={selectedPeriodId}
      selectedPeriodValue={selectedPeriodValue}
      onPeriodChange={handlePeriodChange}
      onBulkAction={handleBulkAction}
      loading={loading}
    />
  )
}