"use client"

import * as React from "react"
import {
  type ColumnDef,
  type ColumnFiltersState,
  type SortingState,
  type RowSelectionState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { ArrowUpDown, Filter, Users, UserCheck, Calendar, Building2, CheckCircle, MoreHorizontal, User, ChevronDown } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { StatusBadge } from "@/components/status-badge"
import { Checkbox } from "@/components/ui/checkbox"
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card"
import { useIsMobile } from "@/components/ui/use-mobile"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu"
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet"
import { PeriodMonthFilter } from "@/components/period-month-filter"
import type { EmployeeAppraisal, UserRole, AppraisalStatus, Manager, AppraisalPeriod } from "@/lib/types"
import Link from "next/link"
import { debug } from "@/lib/debug"

interface AppraisalDashboardTableProps {
  data: EmployeeAppraisal[]
  managers: Manager[]
  periods?: AppraisalPeriod[]
  selectedPeriodId?: string
  selectedPeriodValue?: string // YYYY-MM format for period filter
  onPeriodChange?: (periodValue: string, month: number, year: number) => void
  onRefresh?: () => void
  onBulkAction?: (action: string, selectedIds: string[]) => void
  loading?: boolean
}

// Helper function to get role badge color
function getRoleBadgeVariant(role: UserRole): "default" | "secondary" | "destructive" | "outline" {
  switch (role) {
    case 'super-admin':
      return 'destructive'
    case 'hr-admin':
    case 'admin':
      return 'default'
    case 'manager':
      return 'secondary'
    case 'accountant':
      return 'outline'
    default:
      return 'outline'
  }
}

// Helper function to safely format dates for hydration
function formatDateSafe(dateString: string | undefined): string {
  if (!dateString) return '-'

  try {
    const date = new Date(dateString)
    // Use a consistent format that works on both server and client
    return date.toISOString().split('T')[0] // YYYY-MM-DD format
  } catch (error) {
    console.error('Date formatting error:', error)
    return '-'
  }
}

export function AppraisalDashboardTable({
  data,
  managers,
  periods = [],
  selectedPeriodId,
  selectedPeriodValue,
  onPeriodChange,
  onRefresh,
  onBulkAction,
  loading = false
}: AppraisalDashboardTableProps) {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [rowSelection, setRowSelection] = React.useState<RowSelectionState>({})
  const [selectedManagerIds, setSelectedManagerIds] = React.useState<string[]>([])
  const [managerSearchTerm, setManagerSearchTerm] = React.useState<string>("")
  const [filtersOpen, setFiltersOpen] = React.useState(false)
  const isMobile = useIsMobile()

  // Custom filter function for managers
  const managerFilter = React.useCallback((row: any, columnId: string, filterValue: string[]) => {
    if (!filterValue || filterValue.length === 0) {
      return true
    }

    const appraisal = row.original as EmployeeAppraisal
    // Check if the employee's manager ID is in the selected manager IDs
    // We need to check both managerId (legacy) and managerIds (multi-manager) fields
    const employeeManagerId = appraisal.managerName ? 
      managers.find(m => m.fullName === appraisal.managerName)?.id : null
    
    return employeeManagerId ? filterValue.includes(employeeManagerId) : false
  }, [managers])

  // Mobile card component for responsive design
  const MobileAppraisalCard = ({ appraisal }: { appraisal: EmployeeAppraisal }) => (
    <Card key={appraisal.employeeId} className="mb-4">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="space-y-1 flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <Checkbox
                checked={rowSelection[appraisal.employeeId] || false}
                onCheckedChange={(checked) => {
                  setRowSelection(prev => ({
                    ...prev,
                    [appraisal.employeeId]: !!checked
                  }))
                }}
                aria-label={`Select ${appraisal.fullName}`}
                className="touch-target-sm"
              />
              <h3 className="mobile-heading-2 leading-none truncate">{appraisal.fullName}</h3>
            </div>
            <p className="mobile-caption text-muted-foreground truncate">{appraisal.departmentName}</p>
            {appraisal.managerName && (
              <p className="mobile-caption text-muted-foreground truncate">
                Reports to: {appraisal.managerName}
              </p>
            )}
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="touch-target flex-shrink-0"
                aria-label={`Actions for ${appraisal.fullName}`}
              >
                <span className="sr-only">Open menu for {appraisal.fullName}</span>
                <MoreHorizontal className="h-5 w-5" aria-hidden="true" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuLabel>Actions for {appraisal.fullName}</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/employees/${appraisal.employeeId}/profile`} className="flex items-center touch-target">
                  <User className="mr-2 h-4 w-4" />
                  View Profile
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/appraisal/${appraisal.employeeId}`} className="flex items-center touch-target">
                  <MoreHorizontal className="mr-2 h-4 w-4" />
                  View Appraisal
                </Link>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="mobile-caption font-medium text-muted-foreground">Status:</span>
            <StatusBadge status={appraisal.status} />
          </div>
          <div className="flex items-center justify-between">
            <span className="mobile-caption font-medium text-muted-foreground">Submitted:</span>
            <span className="mobile-body text-muted-foreground">
              {formatDateSafe(appraisal.submittedAt)}
            </span>
          </div>
          {appraisal.role && (
            <div className="flex items-center justify-between">
              <span className="mobile-caption font-medium text-muted-foreground">Role:</span>
              <Badge variant={getRoleBadgeVariant(appraisal.role)}>
                {appraisal.role}
              </Badge>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )

  debug.log('🔍 [DEBUG] AppraisalDashboardTable - Received data:', data.map(item => ({
    fullName: item.fullName,
    departmentName: item.departmentName,
    status: item.status,
    role: item.role,
    isManager: item.isManager,
    managerName: item.managerName
  })))

  // Get unique values for filters
  const departments = React.useMemo(() => {
    const depts = Array.from(new Set(data.map(item => item.departmentName).filter(Boolean)))
    debug.log('🏢 [DEBUG] AppraisalDashboardTable - Unique departments:', depts)
    return depts.sort()
  }, [data])

  const roles = React.useMemo(() => {
    const roleList = Array.from(new Set(data.map(item => item.role).filter(Boolean)))
    debug.log('👔 [DEBUG] AppraisalDashboardTable - Unique roles:', roleList)
    return roleList.sort()
  }, [data])

  const statuses = React.useMemo(() => {
    const statusList = Array.from(new Set(data.map(item => item.status)))
    debug.log('📊 [DEBUG] AppraisalDashboardTable - Unique statuses:', statusList)
    return statusList.sort()
  }, [data])

  const columns: ColumnDef<EmployeeAppraisal>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "fullName",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-semibold"
        >
          <Users className="mr-2 h-4 w-4" />
          Employee
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <div className="flex flex-col">
          <Link 
            href={`/dashboard/employees/${row.original.employeeId}/profile`}
            className="font-medium text-blue-600 hover:text-blue-800 hover:underline"
          >
            {row.getValue("fullName")}
          </Link>
          {row.original.managerName && (
            <span className="text-xs text-muted-foreground">
              Reports to: {row.original.managerName}
            </span>
          )}
        </div>
      ),
    },
    {
      accessorKey: "departmentName",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-semibold"
        >
          <Building2 className="mr-2 h-4 w-4" />
          Department
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <span className="text-sm">{row.getValue("departmentName")}</span>
      ),
    },
    {
      accessorKey: "role",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-semibold"
        >
          Role
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const role = row.getValue("role") as UserRole
        return role ? (
          <Badge variant={getRoleBadgeVariant(role)} className="text-xs">
            {role.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </Badge>
        ) : (
          <span className="text-muted-foreground text-xs">N/A</span>
        )
      },
      filterFn: managerFilter,
    },
    {
      accessorKey: "isManager",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-semibold"
        >
          <UserCheck className="mr-2 h-4 w-4" />
          Manager
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const isManager = row.getValue("isManager") as boolean
        return (
          <Badge variant={isManager ? "default" : "outline"} className="text-xs">
            {isManager ? "Yes" : "No"}
          </Badge>
        )
      },
    },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-semibold"
        >
          Status
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const status = row.getValue("status") as AppraisalStatus
        console.log('🏷️ [DEBUG] StatusBadge rendering for status:', status)
        return <StatusBadge status={status} />
      },
    },
    {
      accessorKey: "submittedAt",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-semibold"
        >
          <Calendar className="mr-2 h-4 w-4" />
          Submitted
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const submittedAt = row.getValue("submittedAt") as string | undefined
        const formattedDate = formatDateSafe(submittedAt)

        return (
          <span className={`text-sm ${formattedDate === '-' ? 'text-muted-foreground' : ''}`}>
            {formattedDate}
          </span>
        )
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Button size="sm" asChild>
            <Link href={`/dashboard/appraisal/${row.original.employeeId}`}>
              {row.original.status === "not-started" ? "Start" : "View"}
            </Link>
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Quick Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/employees/${row.original.employeeId}/profile`} className="flex items-center">
                  <User className="mr-2 h-4 w-4" />
                  View Profile
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/appraisal/${row.original.employeeId}`} className="flex items-center">
                  <CheckCircle className="mr-2 h-4 w-4" />
                  {row.original.status === "not-started" ? "Start Appraisal" : "View Appraisal"}
                </Link>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
    },
  ]

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onRowSelectionChange: setRowSelection,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    state: { sorting, columnFilters, rowSelection },
    enableRowSelection: true,
    getRowId: (row) => row.employeeId,
  })

  // Apply manager filter when selection changes
  React.useEffect(() => {
    if (selectedManagerIds.length > 0) {
      table.getColumn("role")?.setFilterValue(selectedManagerIds)
    } else {
      table.getColumn("role")?.setFilterValue(undefined)
    }
  }, [selectedManagerIds, table])

  // Get selected rows
  const selectedRows = table.getFilteredSelectedRowModel().rows
  const selectedIds = selectedRows.map(row => row.original.employeeId)

  const handleBulkAction = (action: string) => {
    if (onBulkAction && selectedIds.length > 0) {
      onBulkAction(action, selectedIds)
      setRowSelection({})
    }
  }

  return (
    <div className="space-y-4">
      {/* Bulk Actions */}
      {selectedIds.length > 0 && (
        <div className="flex items-center gap-4 p-4 bg-muted rounded-lg">
          <div className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4 text-primary" />
            <span className="text-sm font-medium">
              {selectedIds.length} item{selectedIds.length === 1 ? '' : 's'} selected
            </span>
          </div>
          <div className="flex items-center gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <MoreHorizontal className="h-4 w-4 mr-2" />
                  Bulk Actions
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>Actions for {selectedIds.length} items</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => handleBulkAction('approve')}>
                  Approve Selected
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleBulkAction('submit')}>
                  Submit Selected Drafts
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleBulkAction('ready-to-pay')}>
                  Mark Ready to Pay
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleBulkAction('contact-manager')}>
                  Mark Contact Manager
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => handleBulkAction('export')}>
                  Export Selected
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => setRowSelection({})}
            >
              Clear Selection
            </Button>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="flex flex-wrap items-center gap-3 sm:gap-4">
        {/* Grouped filters trigger - Sheet on mobile, Dropdown on desktop */}
        {isMobile ? (
          <Sheet open={filtersOpen} onOpenChange={setFiltersOpen}>
            <SheetTrigger asChild>
              <Button variant="outline" className="touch-target min-h-[44px]">
                <Filter className="mr-2 h-4 w-4" />
                Filters
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </SheetTrigger>
            <SheetContent side="bottom" className="h-[85vh] p-0">
              <SheetHeader className="px-4 pt-4 pb-2 border-b">
                <SheetTitle>Filters</SheetTitle>
              </SheetHeader>
              <div className="p-4">
                <div className="max-h-[66vh] overflow-y-auto space-y-3">
                  {/* Period */}
                  {onPeriodChange && (
                    <div>
                      <div className="text-xs font-medium text-muted-foreground mb-1">Period</div>
                      <PeriodMonthFilter
                        value={selectedPeriodValue}
                        onChange={(value, month, year) => onPeriodChange(value, month, year)}
                        disabled={loading}
                        showPeriodStatus={true}
                        showExternalBadges={false}
                        existingPeriods={periods.map(p => {
                          const d = new Date(p.periodStart)
                          const y = d.getFullYear()
                          const m = d.getMonth() + 1
                          return `${y}-${String(m).padStart(2, '0')}`
                        })}
                      />
                    </div>
                  )}

                  {/* Keyword */}
                  <div>
                    <div className="text-xs font-medium text-muted-foreground mb-1">Search</div>
                    <Input
                      placeholder="Search employees..."
                      value={(table.getColumn("fullName")?.getFilterValue() as string) ?? ""}
                      onChange={(event) => table.getColumn("fullName")?.setFilterValue(event.target.value)}
                      className="h-10"
                      disabled={loading}
                    />
                  </div>

                  {/* Department */}
                  <div>
                    <div className="text-xs font-medium text-muted-foreground mb-1">Department</div>
                    <Select
                      value={(table.getColumn("departmentName")?.getFilterValue() as string) ?? ""}
                      onValueChange={(value) => table.getColumn("departmentName")?.setFilterValue(value === "all" ? "" : value)}
                      disabled={loading}
                    >
                      <SelectTrigger className="h-10">
                        <SelectValue placeholder="All Departments" />
                      </SelectTrigger>
                      <SelectContent className="max-h-60">
                        <SelectItem value="all">All Departments</SelectItem>
                        {departments.map((dept) => (
                          <SelectItem key={dept} value={dept}>{dept}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Managers multi-select */}
                  <div>
                    <div className="text-xs font-medium text-muted-foreground mb-1">Managers</div>
                    {managers.length > 8 && (
                      <div className="mb-2">
                        <Input
                          placeholder="Search managers..."
                          value={managerSearchTerm}
                          onChange={(e) => setManagerSearchTerm(e.target.value)}
                          className="h-9 text-sm"
                        />
                      </div>
                    )}
                    {managers.length > 3 && (
                      <div className="flex items-center gap-2 mb-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 px-2"
                          onClick={() => {
                            const filtered = managers.filter(m =>
                              managerSearchTerm === "" ||
                              m.fullName.toLowerCase().includes(managerSearchTerm.toLowerCase()) ||
                              (m.departmentName && m.departmentName.toLowerCase().includes(managerSearchTerm.toLowerCase()))
                            )
                            const allSel = filtered.every(m => selectedManagerIds.includes(m.id))
                            if (allSel) setSelectedManagerIds(prev => prev.filter(id => !filtered.some(m => m.id === id)))
                            else setSelectedManagerIds(prev => [...new Set([...prev, ...filtered.map(m => m.id)])])
                          }}
                        >
                          {managers.filter(m =>
                            managerSearchTerm === "" ||
                            m.fullName.toLowerCase().includes(managerSearchTerm.toLowerCase()) ||
                            (m.departmentName && m.departmentName.toLowerCase().includes(managerSearchTerm.toLowerCase()))
                          ).every(m => selectedManagerIds.includes(m.id)) ? 'Deselect All' : 'Select All'}
                        </Button>
                        {selectedManagerIds.length > 0 && (
                          <Button variant="ghost" size="sm" className="h-8 px-2" onClick={() => setSelectedManagerIds([])}>
                            Clear
                          </Button>
                        )}
                      </div>
                    )}
                    <div className="max-h-[40vh] overflow-y-auto border rounded-md p-2">
                      {managers
                        .filter(m =>
                          managerSearchTerm === "" ||
                          m.fullName.toLowerCase().includes(managerSearchTerm.toLowerCase()) ||
                          (m.departmentName && m.departmentName.toLowerCase().includes(managerSearchTerm.toLowerCase()))
                        )
                        .map((manager) => (
                          <label key={manager.id} className="flex items-start gap-2 py-2 cursor-pointer">
                            <input
                              type="checkbox"
                              className="mt-1"
                              checked={selectedManagerIds.includes(manager.id)}
                              onChange={(e) => {
                                if (e.target.checked) setSelectedManagerIds(prev => [...prev, manager.id])
                                else setSelectedManagerIds(prev => prev.filter(id => id !== manager.id))
                              }}
                            />
                            <div className="flex flex-col min-w-0">
                              <span className="text-sm font-medium truncate">{manager.fullName}</span>
                              {manager.departmentName && (
                                <span className="text-xs text-muted-foreground truncate">{manager.departmentName}</span>
                              )}
                            </div>
                          </label>
                        ))}
                    </div>
                  </div>
                </div>
                <div className="pt-3 flex justify-end">
                  <Button variant="outline" onClick={() => setFiltersOpen(false)}>Close</Button>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        ) : (
          <DropdownMenu onOpenChange={(open) => { if (!open) setManagerSearchTerm("") }}>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="touch-target min-h-[44px]">
                <Filter className="mr-2 h-4 w-4" />
                Filters
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-[92vw] sm:w-[520px] max-w-[520px] max-h-[70vh] overflow-y-auto p-2" align="start" sideOffset={6}>
              {/* Period */}
              {onPeriodChange && (
                <div className="px-2 py-2">
                  <div className="text-xs font-medium text-muted-foreground mb-1">Period</div>
                  <PeriodMonthFilter
                    value={selectedPeriodValue}
                    onChange={(value, month, year) => onPeriodChange(value, month, year)}
                    disabled={loading}
                    showPeriodStatus={true}
                    showExternalBadges={false}
                    existingPeriods={periods.map(p => {
                      const d = new Date(p.periodStart)
                      const y = d.getFullYear()
                      const m = d.getMonth() + 1
                      return `${y}-${String(m).padStart(2, '0')}`
                    })}
                  />
                </div>
              )}

              <DropdownMenuSeparator />

              {/* Keyword */}
              <div className="px-2 py-2">
                <div className="text-xs font-medium text-muted-foreground mb-1">Search</div>
                <Input
                  placeholder="Search employees..."
                  value={(table.getColumn("fullName")?.getFilterValue() as string) ?? ""}
                  onChange={(event) => table.getColumn("fullName")?.setFilterValue(event.target.value)}
                  className="h-9"
                  disabled={loading}
                />
              </div>

              {/* Department */}
              <div className="px-2 py-2">
                <div className="text-xs font-medium text-muted-foreground mb-1">Department</div>
                <Select
                  value={(table.getColumn("departmentName")?.getFilterValue() as string) ?? ""}
                  onValueChange={(value) => table.getColumn("departmentName")?.setFilterValue(value === "all" ? "" : value)}
                  disabled={loading}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="All Departments" />
                  </SelectTrigger>
                  <SelectContent className="max-h-60">
                    <SelectItem value="all">All Departments</SelectItem>
                    {departments.map((dept) => (
                      <SelectItem key={dept} value={dept}>{dept}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Managers multi-select */}
              <div className="px-2 py-2">
                <div className="text-xs font-medium text-muted-foreground mb-1">Managers</div>
                {managers.length > 8 && (
                  <div className="mb-2">
                    <Input
                      placeholder="Search managers..."
                      value={managerSearchTerm}
                      onChange={(e) => setManagerSearchTerm(e.target.value)}
                      className="h-8 text-sm"
                    />
                  </div>
                )}
                {managers.length > 3 && (
                  <div className="flex items-center gap-2 mb-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 px-2"
                      onClick={() => {
                        const filtered = managers.filter(m =>
                          managerSearchTerm === "" ||
                          m.fullName.toLowerCase().includes(managerSearchTerm.toLowerCase()) ||
                          (m.departmentName && m.departmentName.toLowerCase().includes(managerSearchTerm.toLowerCase()))
                        )
                        const allSel = filtered.every(m => selectedManagerIds.includes(m.id))
                        if (allSel) setSelectedManagerIds(prev => prev.filter(id => !filtered.some(m => m.id === id)))
                        else setSelectedManagerIds(prev => [...new Set([...prev, ...filtered.map(m => m.id)])])
                      }}
                    >
                      {managers.filter(m =>
                        managerSearchTerm === "" ||
                        m.fullName.toLowerCase().includes(managerSearchTerm.toLowerCase()) ||
                        (m.departmentName && m.departmentName.toLowerCase().includes(managerSearchTerm.toLowerCase()))
                      ).every(m => selectedManagerIds.includes(m.id)) ? 'Deselect All' : 'Select All'}
                    </Button>
                    {selectedManagerIds.length > 0 && (
                      <Button variant="ghost" size="sm" className="h-8 px-2" onClick={() => setSelectedManagerIds([])}>
                        Clear
                      </Button>
                    )}
                  </div>
                )}
                <div className="max-h-[220px] overflow-y-auto border rounded-md p-2">
                  {managers
                    .filter(m =>
                      managerSearchTerm === "" ||
                      m.fullName.toLowerCase().includes(managerSearchTerm.toLowerCase()) ||
                      (m.departmentName && m.departmentName.toLowerCase().includes(managerSearchTerm.toLowerCase()))
                    )
                    .map((manager) => (
                      <label key={manager.id} className="flex items-start gap-2 py-1 cursor-pointer">
                        <input
                          type="checkbox"
                          className="mt-1"
                          checked={selectedManagerIds.includes(manager.id)}
                          onChange={(e) => {
                            if (e.target.checked) setSelectedManagerIds(prev => [...prev, manager.id])
                            else setSelectedManagerIds(prev => prev.filter(id => id !== manager.id))
                          }}
                        />
                        <div className="flex flex-col min-w-0">
                          <span className="text-sm font-medium truncate">{manager.fullName}</span>
                          {manager.departmentName && (
                            <span className="text-xs text-muted-foreground truncate">{manager.departmentName}</span>
                          )}
                        </div>
                      </label>
                    ))}
                </div>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        {/* Keep status visible as a single filter */}
        <Select
          value={(table.getColumn("status")?.getFilterValue() as string) ?? ""}
          onValueChange={(value) => table.getColumn("status")?.setFilterValue(value === "all" ? "" : value)}
          disabled={loading}
        >
          <SelectTrigger className="w-full sm:w-[160px] h-10">
            <SelectValue placeholder="All Statuses" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            {statuses.map((status) => (
              <SelectItem key={status} value={status}>
                {status.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Loading overlay */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-pulse text-muted-foreground">Loading appraisals...</div>
        </div>
      )}

      {/* Mobile card view */}
      {!loading && isMobile ? (
        <div className="space-y-4">
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row) => (
              <MobileAppraisalCard key={row.id} appraisal={row.original} />
            ))
          ) : (
            <Card>
              <CardContent className="py-8">
                <p className="text-center text-muted-foreground">No employees found.</p>
              </CardContent>
            </Card>
          )}
        </div>
      ) : !loading ? (
        /* Desktop table view */
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow key={row.id}>
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    No employees found.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      ) : null}

      {/* Pagination */}
      {!loading && (
      <div className="flex items-center justify-between space-x-2 py-4">
        <div className="text-sm text-muted-foreground">
          Showing {table.getFilteredRowModel().rows.length} of {data.length} employees
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
      )}
    </div>
  )
}
