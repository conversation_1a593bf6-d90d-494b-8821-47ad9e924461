# Repository Guidelines

## Project Structure & Module Organization
- `app/`: Next.js App Router pages and API routes.
- `components/`: Reusable UI and feature components.
- `hooks/`: Reusable React hooks.
- `lib/`: Core logic
  - `db/`: domain modules and DB utilities
  - `actions/`: server actions
  - `data/`, `services/`, `constants/`, `utils.ts`
- `public/`: static assets.
- `styles/`: Tailwind/global CSS.
- `scripts/`: one-off maintenance/migrations.
- Tests colocated using `__tests__/` or `*.test|spec.ts(x)`.

## Build, Test, and Development Commands
- `npm run dev`: Start Next.js dev server at `http://localhost:3000`.
- `npm run build`: Production build.
- `npm start`: Serve the production build.
- `npm run lint`: Run Next.js/ESLint checks.
- `npm test`: Run Jest tests.
- `npm run test:watch` / `test:coverage` / `test:ci`: Watch, coverage, CI mode.

## Coding Style & Naming Conventions
- Language: TypeScript (strict). Indentation: 2 spaces.
- Filenames: kebab-case (e.g., `employee-profile.tsx`, `use-loading-state.ts`).
- Components: React function components; export named when practical; PascalCase component names.
- Hooks: `useX` naming; colocate with feature or in `hooks/`.
- Styling: Tailwind CSS; prefer utility classes over ad-hoc CSS.
- Linting: Next.js ESLint config; fix issues before PRs.

## Testing Guidelines
- Frameworks: Jest + Testing Library (`jest-environment-jsdom`).
- Coverage: Global thresholds ≥ 70% lines/branches/functions.
- Locations/Patterns: `__tests__/` or `*.test.ts(x)` / `*.spec.ts(x)`.
- Run examples: `npm test`, `npm run test:watch`, `npm run test:coverage`.

## Commit & Pull Request Guidelines
- Commits: Clear, present tense (e.g., "Add appraisal footer state"), small and scoped.
- PRs: Explain what/why, link issues, include screenshots for UI changes, note any DB/email/config changes, and update docs.
- Checks: Ensure `lint`, `build`, and `test` pass locally.

## Security & Configuration Tips
- Copy `.env.example` to `.env.local`; never commit secrets.
- Required keys: Clerk (`CLERK_*`), Resend, DB URL, cron/admin secrets.
- Avoid real PII in tests/fixtures; use mocks (see `jest.setup.js`).

## Architecture Overview
- Next.js App Router with server actions in `lib/actions` and domain logic in `lib/db/domains/*`.
- Auth: Clerk; Email/notifications via Resend in `lib/services/*`.
